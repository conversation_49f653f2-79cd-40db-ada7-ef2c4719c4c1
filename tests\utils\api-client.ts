export interface ApiResponseTest<T = any> {
  data?: T;
  error?: string;
  status: number;
  ok: boolean;
}

export interface ApiClientOptions {
  baseUrl?: string;
  apiKey?: string;
  headers?: Record<string, string>;
}

export class ApiClient {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;

  constructor(options: ApiClientOptions = {}) {
    this.baseUrl = options.baseUrl || "http://localhost:3000";
    this.defaultHeaders = {
      "Content-Type": "application/json",
      "X-API-Key": options.apiKey || process.env.INTERNAL_API_KEY || "",
      ...options.headers,
    };
  }

  async get<T = any>(
    endpoint: string,
    options: {
      headers?: Record<string, string>;
      searchParams?: Record<string, string>;
    } = {},
  ): Promise<ApiResponseTest<T> & { status: number; ok: boolean }> {
    const url = new URL(`${this.baseUrl}${endpoint}`);

    if (options.searchParams) {
      Object.entries(options.searchParams).forEach(([key, value]) => {
        url.searchParams.append(key, value);
      });
    }

    const response = await fetch(url.toString(), {
      method: "GET",
      headers: {
        ...this.defaultHeaders,
        ...options.headers,
      },
    });

    const data = await response.json();

    return {
      ...data,
      status: response.status,
      ok: response.ok,
    };
  }

  async post<T = any>(
    endpoint: string,
    body?: any,
    options: {
      headers?: Record<string, string>;
    } = {},
  ): Promise<ApiResponseTest<T>> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: "POST",
      headers: {
        ...this.defaultHeaders,
        ...options.headers,
      },
      body: body ? JSON.stringify(body) : undefined,
    });

    const data = await response.json();

    return {
      ...data,
      status: response.status,
      ok: response.ok,
    } as ApiResponseTest<T> & { status: number; ok: boolean };
  }

  setApiKey(apiKey: string) {
    this.defaultHeaders["X-API-Key"] = apiKey;
  }

  setLanguage(language: string) {
    this.defaultHeaders["Accept-Language"] = language;
  }
}

// Default client instance
export const apiClient = new ApiClient();
