import { describe, it, expect, beforeAll } from "vitest";
import * as z from "zod";
import { apiClient } from "../utils/api-client";
import { experienceSchema } from "@/app/(site)/experience/schema";

const experiencesApiResponseSchema = z.object({
  data: z.array(experienceSchema),
});

const errorResponseSchema = z.object({
  error: z.string(),
});

describe("Experiences API Integration Tests", () => {
  describe("GET /api/experiences", () => {
    it("should return a list of experiences with valid schema", async () => {
      const response = await apiClient.get("/api/experiences");

      expect(response.status).toBe(200);
      expect(response.ok).toBe(true);

      // Validate the response structure
      const validationResult = experiencesApiResponseSchema.safeParse(response);

      if (!validationResult.success) {
        console.error(
          "Schema validation failed:",
          z.prettifyError(validationResult.error),
        );
        throw new Error(
          `Response schema validation failed: ${validationResult.error.message}`,
        );
      }

      expect(validationResult.success).toBe(true);
      expect(Array.isArray(validationResult.data.data)).toBe(true);
    });

    it("should return experiences with required fields", async () => {
      const response = await apiClient.get("/api/experiences");

      expect(response.status).toBe(200);
      expect(response.data).toBeDefined();
      expect(Array.isArray(response.data)).toBe(true);

      if (response.data.length > 0) {
        const firstExperience = response.data[0];

        // Check required fields exist
        expect(firstExperience._id).toBeDefined();
        expect(firstExperience._type).toBe("experience");
        expect(firstExperience.name).toBeDefined();
        expect(firstExperience.description).toBeDefined();
        expect(firstExperience.provider).toBeDefined();
        expect(firstExperience.type).toBeDefined();
        expect(firstExperience.locations).toBeDefined();
        expect(Array.isArray(firstExperience.locations)).toBe(true);
        expect(firstExperience.locations.length).toBeGreaterThan(0);
      }
    });

    it("should support language parameter", async () => {
      // Test with Portuguese (default)
      const ptResponse = await apiClient.get("/api/experiences", {
        headers: { "Accept-Language": "pt" },
      });

      expect(ptResponse.status).toBe(200);
      expect(ptResponse.data).toBeDefined();

      // Test with English
      const enResponse = await apiClient.get("/api/experiences", {
        headers: { "Accept-Language": "en" },
      });

      expect(enResponse.status).toBe(200);
      expect(enResponse.data).toBeDefined();
    });

    it("should support filtering parameters", async () => {
      const response = await apiClient.get("/api/experiences", {
        searchParams: {
          type: "LODGING",
          limit: "5",
        },
      });

      expect(response.status).toBe(200);
      expect(response.data).toBeDefined();
      expect(Array.isArray(response.data)).toBe(true);

      // If there are results, they should match the filter
      if (response.data.length > 0) {
        expect(response.data.length).toBeLessThanOrEqual(5);
      }
    });

    it("should return 401 when API key is missing", async () => {
      const clientWithoutKey = new (
        await import("../utils/api-client")
      ).ApiClient({
        apiKey: "",
      });

      const response = await clientWithoutKey.get("/api/experiences");

      expect(response.status).toBe(401);

      const errorValidation = errorResponseSchema.safeParse(response);
      expect(errorValidation.success).toBe(true);
      expect(response.error).toBeDefined();
    });

    it("should return 401 when API key is invalid", async () => {
      const clientWithInvalidKey = new (
        await import("../utils/api-client")
      ).ApiClient({
        apiKey: "invalid-key",
      });

      const response = await clientWithInvalidKey.get("/api/experiences");

      expect(response.status).toBe(401);

      const errorValidation = errorResponseSchema.safeParse(response);
      expect(errorValidation.success).toBe(true);
      expect(response.error).toBeDefined();
    });

    it("should handle server errors gracefully", async () => {
      // This test might need to be adjusted based on how you want to simulate server errors
      // For now, we'll just ensure the API doesn't crash with unusual parameters
      const response = await apiClient.get("/api/experiences", {
        searchParams: {
          invalidParam: "test",
        },
      });

      // Should still return a valid response (either success or controlled error)
      expect([200, 400, 500].includes(response.status)).toBe(true);
    });
  });
});
